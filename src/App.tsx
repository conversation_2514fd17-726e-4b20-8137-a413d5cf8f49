
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Diensten from "./pages/Diensten";
import Tarieven from "./pages/Tarieven";
import Beoordelingen from "./pages/Beoordelingen";
import Contact from "./pages/Contact";
import OverOns from "./pages/OverOns";
import Projecten from "./pages/Projecten";
import AlgemeneVoorwaarden from "./pages/AlgemeneVoorwaarden";
import KlusPlaatsen from "./pages/KlusPlaatsen";
import AfspraakMaken from "./pages/AfspraakMaken";
import Offerte from "./pages/Offerte";
import NotFound from "./pages/NotFound";
import LoodgietersWerk from "./pages/diensten/LoodgietersWerk";
import Elektriciteit from "./pages/diensten/Elektriciteit";
import Schilderwerk from "./pages/diensten/Schilderwerk";
import Montage from "./pages/diensten/Montage";
import Onderhoud from "./pages/diensten/Onderhoud";
import Tegels from "./pages/diensten/Tegels";
import Tuinonderhoud from "./pages/diensten/Tuinonderhoud";
import Verhuizing from "./pages/diensten/Verhuizing";
import Schoonmaak from "./pages/diensten/Schoonmaak";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/diensten" element={<Diensten />} />
          <Route path="/diensten/loodgieterswerk" element={<LoodgietersWerk />} />
          <Route path="/diensten/elektriciteit" element={<Elektriciteit />} />
          <Route path="/diensten/schilderwerk" element={<Schilderwerk />} />
          <Route path="/diensten/montage" element={<Montage />} />
          <Route path="/diensten/onderhoud" element={<Onderhoud />} />
          <Route path="/diensten/tegels" element={<Tegels />} />
          <Route path="/diensten/tuinonderhoud" element={<Tuinonderhoud />} />
          <Route path="/diensten/verhuizing" element={<Verhuizing />} />
          <Route path="/diensten/schoonmaak" element={<Schoonmaak />} />
          <Route path="/tarieven" element={<Tarieven />} />
          <Route path="/beoordelingen" element={<Beoordelingen />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/over-ons" element={<OverOns />} />
          <Route path="/projecten" element={<Projecten />} />
          <Route path="/algemene-voorwaarden" element={<AlgemeneVoorwaarden />} />
          <Route path="/klus-plaatsen" element={<KlusPlaatsen />} />
          <Route path="/offerte" element={<Offerte />} />
          <Route path="/afspraak-maken" element={<AfspraakMaken />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
