
import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-secondary-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 bg-gradient-to-r from-primary-600 to-primary-800 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">AS</span>
              </div>
              <span className="text-xl font-bold">ASklussen.nl</span>
            </div>
            <p className="text-gray-300 mb-4">
              Uw betrouwbare partner voor alle klussen in en om het huis. 
              Vakmanschap, kwaliteit en service staan bij ons centraal.
            </p>
            <div className="space-y-2 text-sm">
              <p>📧 <EMAIL></p>
              <p>📞 06-12345678</p>
              <p>📍 Nederland</p>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Snelle Links</h3>
            <ul className="space-y-2">
              <li><Link to="/diensten" className="text-gray-300 hover:text-white transition-colors">Diensten</Link></li>
              <li><Link to="/tarieven" className="text-gray-300 hover:text-white transition-colors">Tarieven</Link></li>
              <li><Link to="/projecten" className="text-gray-300 hover:text-white transition-colors">Projecten</Link></li>
              <li><Link to="/beoordelingen" className="text-gray-300 hover:text-white transition-colors">Beoordelingen</Link></li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Diensten</h3>
            <ul className="space-y-2 text-sm">
              <li className="text-gray-300">Loodgieterswerk</li>
              <li className="text-gray-300">Elektriciteit</li>
              <li className="text-gray-300">Schilderwerk</li>
              <li className="text-gray-300">Montage</li>
              <li className="text-gray-300">Onderhoud</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 ASklussen.nl. Alle rechten voorbehouden.
          </p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <Link to="/algemene-voorwaarden" className="text-gray-400 hover:text-white text-sm transition-colors">
              Algemene Voorwaarden
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
