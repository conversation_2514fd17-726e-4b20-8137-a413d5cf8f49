
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { toast } from '@/hooks/use-toast';
import { Check, Upload, Clock, Euro, Shield, Phone } from 'lucide-react';

const Offerte = () => {
  const [formData, setFormData] = useState({
    services: [],
    voornaam: '',
    achternaam: '',
    email: '',
    telefoon: '',
    straat: '',
    huisnummer: '',
    postcode: '',
    stad: '',
    beschrijving: '',
    urgentie: 'normaal',
    voor<PERSON>uren: '',
    akkoord: false
  });

  const services = [
    { id: 'loodgieterswerk', name: 'Loodgieterswerk', icon: '🔧', tarief: '€45-55/uur' },
    { id: 'elektriciteit', name: 'Elektriciteit', icon: '⚡', tarief: '€50-60/uur' },
    { id: 'schilderwerk', name: 'Schilderwerk', icon: '🎨', tarief: '€35-45/uur' },
    { id: 'montage', name: 'Montage & Assemblage', icon: '🔨', tarief: '€40-50/uur' },
    { id: 'onderhoud', name: 'Onderhoud & Reparatie', icon: '🛠️', tarief: '€38-48/uur' },
    { id: 'tegels', name: 'Tegels & Vloeren', icon: '🏠', tarief: '€42-52/uur' },
    { id: 'tuinonderhoud', name: 'Tuinonderhoud', icon: '🌱', tarief: '€30-40/uur' },
    { id: 'verhuizing', name: 'Verhuizing', icon: '🚚', tarief: '€65-85/uur' },
    { id: 'schoonmaak', name: 'Schoonmaak', icon: '🧽', tarief: '€25-35/uur' }
  ];

  const faqItems = [
    {
      question: "Hoe snel ontvang ik mijn offerte?",
      answer: "U ontvangt binnen 24 uur een vrijblijvende offerte via e-mail. Voor spoedklussen kunnen we vaak binnen 2 uur reageren."
    },
    {
      question: "Zijn de offertes echt gratis?",
      answer: "Ja, alle offertes zijn volledig gratis en vrijblijvend. Er zijn geen verborgen kosten."
    },
    {
      question: "Wat gebeurt er na het versturen van mijn aanvraag?",
      answer: "Na ontvangst van uw aanvraag nemen wij contact met u op om eventuele vragen te stellen. Vervolgens ontvangt u een gedetailleerde offerte met alle kosten."
    },
    {
      question: "Kan ik de offerte aanpassen?",
      answer: "Natuurlijk! We bespreken graag uw wensen en passen de offerte hierop aan."
    },
    {
      question: "Hoe worden de tarieven berekend?",
      answer: "Onze tarieven zijn gebaseerd op het type werk, benodigde materialen, tijdsduur en complexiteit van de klus."
    },
    {
      question: "Bieden jullie garantie op het werk?",
      answer: "Ja, wij bieden standaard garantie op alle uitgevoerde werkzaamheden volgens onze algemene voorwaarden."
    }
  ];

  const handleServiceChange = (serviceId) => {
    setFormData(prev => ({
      ...prev,
      services: prev.services.includes(serviceId)
        ? prev.services.filter(id => id !== serviceId)
        : [...prev.services, serviceId]
    }));
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (formData.services.length === 0) {
      toast({
        title: "Selecteer minimaal één dienst",
        variant: "destructive"
      });
      return;
    }

    if (!formData.voornaam || !formData.achternaam || !formData.email || !formData.telefoon || !formData.beschrijving) {
      toast({
        title: "Vul alle verplichte velden in",
        variant: "destructive"
      });
      return;
    }

    if (!formData.akkoord) {
      toast({
        title: "Ga akkoord met de algemene voorwaarden",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "Offerte aanvraag verzonden!",
      description: "We nemen binnen 24 uur contact met u op."
    });

    // Reset form
    setFormData({
      services: [],
      voornaam: '',
      achternaam: '',
      email: '',
      telefoon: '',
      straat: '',
      huisnummer: '',
      postcode: '',
      stad: '',
      beschrijving: '',
      urgentie: 'normaal',
      voorkeuren: '',
      akkoord: false
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      <div className="pt-20 pb-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12 animate-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Gratis <span className="text-primary-600">Offerte</span> Aanvragen
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Ontvang binnen 24 uur een vrijblijvende, gedetailleerde offerte op maat
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12">
              <div className="flex items-center justify-center space-x-3 p-4 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-200">
                <Clock className="w-8 h-8 text-primary-600" />
                <div className="text-left">
                  <div className="font-semibold text-gray-900">24 uur reactie</div>
                  <div className="text-sm text-gray-600">Snelle response</div>
                </div>
              </div>
              <div className="flex items-center justify-center space-x-3 p-4 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-200">
                <Euro className="w-8 h-8 text-accent-500" />
                <div className="text-left">
                  <div className="font-semibold text-gray-900">Gratis offerte</div>
                  <div className="text-sm text-gray-600">Geen verborgen kosten</div>
                </div>
              </div>
              <div className="flex items-center justify-center space-x-3 p-4 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-200">
                <Shield className="w-8 h-8 text-green-600" />
                <div className="text-left">
                  <div className="font-semibold text-gray-900">Garantie</div>
                  <div className="text-sm text-gray-600">Op alle werkzaamheden</div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Offerte Formulier */}
            <div className="lg:col-span-2">
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl text-gray-900">Offerte Aanvraag</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Diensten Selectie */}
                    <div>
                      <Label className="text-lg font-semibold mb-4 block">Welke diensten heeft u nodig? *</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {services.map((service) => (
                          <div
                            key={service.id}
                            onClick={() => handleServiceChange(service.id)}
                            className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                              formData.services.includes(service.id)
                                ? 'border-primary-600 bg-primary-50'
                                : 'border-gray-200 hover:border-primary-300 hover:bg-gray-50'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <span className="text-2xl">{service.icon}</span>
                                <div>
                                  <div className="font-medium text-gray-900">{service.name}</div>
                                  <div className="text-sm text-gray-600">{service.tarief}</div>
                                </div>
                              </div>
                              {formData.services.includes(service.id) && (
                                <Check className="w-5 h-5 text-primary-600" />
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Persoonlijke Gegevens */}
                    <div>
                      <Label className="text-lg font-semibold mb-4 block">Persoonlijke Gegevens</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="voornaam">Voornaam *</Label>
                          <Input
                            id="voornaam"
                            value={formData.voornaam}
                            onChange={(e) => handleInputChange('voornaam', e.target.value)}
                            placeholder="Uw voornaam"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="achternaam">Achternaam *</Label>
                          <Input
                            id="achternaam"
                            value={formData.achternaam}
                            onChange={(e) => handleInputChange('achternaam', e.target.value)}
                            placeholder="Uw achternaam"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="email">E-mailadres *</Label>
                          <Input
                            id="email"
                            type="email"
                            value={formData.email}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            placeholder="<EMAIL>"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="telefoon">Telefoonnummer *</Label>
                          <Input
                            id="telefoon"
                            value={formData.telefoon}
                            onChange={(e) => handleInputChange('telefoon', e.target.value)}
                            placeholder="06-12345678"
                            className="mt-1"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Adresgegevens */}
                    <div>
                      <Label className="text-lg font-semibold mb-4 block">Adres van de klus</Label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="md:col-span-2">
                          <Label htmlFor="straat">Straatnaam</Label>
                          <Input
                            id="straat"
                            value={formData.straat}
                            onChange={(e) => handleInputChange('straat', e.target.value)}
                            placeholder="Straatnaam"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="huisnummer">Huisnummer</Label>
                          <Input
                            id="huisnummer"
                            value={formData.huisnummer}
                            onChange={(e) => handleInputChange('huisnummer', e.target.value)}
                            placeholder="123"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="postcode">Postcode</Label>
                          <Input
                            id="postcode"
                            value={formData.postcode}
                            onChange={(e) => handleInputChange('postcode', e.target.value)}
                            placeholder="1234 AB"
                            className="mt-1"
                          />
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor="stad">Stad</Label>
                          <Input
                            id="stad"
                            value={formData.stad}
                            onChange={(e) => handleInputChange('stad', e.target.value)}
                            placeholder="Amsterdam"
                            className="mt-1"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Beschrijving */}
                    <div>
                      <Label htmlFor="beschrijving" className="text-lg font-semibold">Beschrijving van de klus *</Label>
                      <Textarea
                        id="beschrijving"
                        value={formData.beschrijving}
                        onChange={(e) => handleInputChange('beschrijving', e.target.value)}
                        placeholder="Beschrijf zo gedetailleerd mogelijk wat u gedaan wilt hebben..."
                        className="mt-1 min-h-32"
                        rows={6}
                      />
                    </div>

                    {/* Urgentie */}
                    <div>
                      <Label className="text-lg font-semibold mb-2 block">Urgentie</Label>
                      <div className="flex space-x-4">
                        {[
                          { value: 'normaal', label: 'Normaal', desc: 'Binnen 1-2 weken' },
                          { value: 'urgent', label: 'Urgent', desc: 'Binnen 1 week' },
                          { value: 'spoed', label: 'Spoed', desc: 'Zo snel mogelijk' }
                        ].map((option) => (
                          <div
                            key={option.value}
                            onClick={() => handleInputChange('urgentie', option.value)}
                            className={`p-3 border-2 rounded-lg cursor-pointer flex-1 text-center transition-all ${
                              formData.urgentie === option.value
                                ? 'border-primary-600 bg-primary-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <div className="font-medium">{option.label}</div>
                            <div className="text-sm text-gray-600">{option.desc}</div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Voorkeuren */}
                    <div>
                      <Label htmlFor="voorkeuren" className="text-lg font-semibold">Bijzondere wensen of voorkeuren</Label>
                      <Textarea
                        id="voorkeuren"
                        value={formData.voorkeuren}
                        onChange={(e) => handleInputChange('voorkeuren', e.target.value)}
                        placeholder="Bijvoorbeeld: gewenste uitvoering datum, materiaalvoorkeur, etc."
                        className="mt-1"
                        rows={3}
                      />
                    </div>

                    {/* Akkoord */}
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="akkoord"
                        checked={formData.akkoord}
                        onCheckedChange={(checked) => handleInputChange('akkoord', checked)}
                      />
                      <Label htmlFor="akkoord" className="text-sm">
                        Ik ga akkoord met de{' '}
                        <a href="/algemene-voorwaarden" className="text-primary-600 hover:underline">
                          algemene voorwaarden
                        </a>{' '}
                        en het verwerken van mijn gegevens voor het opstellen van een offerte. *
                      </Label>
                    </div>

                    <Alert>
                      <Shield className="h-4 w-4" />
                      <AlertDescription>
                        Uw gegevens worden vertrouwelijk behandeld en niet gedeeld met derden. 
                        Na het versturen ontvangt u binnen 24 uur een gedetailleerde offerte.
                      </AlertDescription>
                    </Alert>

                    <Button
                      type="submit"
                      className="w-full bg-accent-500 hover:bg-accent-600 text-white py-3 text-lg font-semibold"
                    >
                      Gratis Offerte Aanvragen
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar met FAQ */}
            <div className="space-y-6">
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-900 flex items-center">
                    <Phone className="w-5 h-5 mr-2 text-primary-600" />
                    Direct Contact
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <p className="text-gray-600">Liever direct contact?</p>
                    <div className="space-y-2">
                      <div className="font-semibold text-gray-900">📞 06-12345678</div>
                      <div className="font-semibold text-gray-900">📧 <EMAIL></div>
                    </div>
                    <p className="text-sm text-gray-500">
                      Bereikbaar ma-vr 8:00-18:00<br />
                      Za 9:00-17:00
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-900">Veelgestelde Vragen</CardTitle>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    {faqItems.map((item, index) => (
                      <AccordionItem key={index} value={`item-${index}`}>
                        <AccordionTrigger className="text-left">{item.question}</AccordionTrigger>
                        <AccordionContent className="text-gray-600">
                          {item.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Offerte;
