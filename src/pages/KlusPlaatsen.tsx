
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { toast } from '@/hooks/use-toast';
import { Check, Upload, Users, ArrowLeft, ArrowRight, Shield, Clock } from 'lucide-react';

const KlusPlaatsen = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    dienst: '',
    subspecialiteit: '',
    voornaam: '',
    achternaam: '',
    email: '',
    telefoon: '',
    straat: '',
    huisnummer: '',
    postcode: '',
    stad: '',
    beschrijving: '',
    urgentie: 'normaal',
    voor<PERSON>uren: '',
    fotos: [],
    akkoord: false
  });

  const diensten = {
    loodgieterswerk: {
      name: 'Loodgieterswerk',
      icon: '🔧',
      tarief: '€45-55/uur',
      subspecialiteiten: [
        'Lekkage reparatie',
        'Kraan vervangen/installeren',
        'Toilet reparatie/vervangen',
        'Verstoppingen verhelpen',
        'CV-ketel onderhoud',
        'Radiator installatie',
        'Waterleiding aanleggen',
        'Badkamer renovatie',
        'Douchecabine installeren',
        'Overige loodgieterswerk'
      ]
    },
    elektriciteit: {
      name: 'Elektriciteit',
      icon: '⚡',
      tarief: '€50-60/uur',
      subspecialiteiten: [
        'Stopcontact installeren',
        'Schakelaar vervangen',
        'Lamp ophangen',
        'Verlichting installeren',
        'Groepenkast uitbreiden',
        'Storing oplossen',
        'Periodieke keuring',
        'Smart home installatie',
        'Buitenverlichting',
        'Overige elektriciteitswerk'
      ]
    },
    schilderwerk: {
      name: 'Schilderwerk',
      icon: '🎨',
      tarief: '€35-45/uur',
      subspecialiteiten: [
        'Muren schilderen',
        'Plafond schilderen',
        'Houtwerk schilderen',
        'Kozijnen schilderen',
        'Behang aanbrengen',
        'Behang verwijderen',
        'Gevel schilderen',
        'Decoratief schilderen',
        'Voorbehandeling',
        'Overige schilderwerk'
      ]
    },
    montage: {
      name: 'Montage & Assemblage',
      icon: '🔨',
      tarief: '€40-50/uur',
      subspecialiteiten: [
        'IKEA meubels monteren',
        'Keuken monteren',
        'TV ophangen',
        'Wandkast ophangen',
        'Tuinmeubilair assembleren',
        'Fitnessapparatuur',
        'Speeltoestel bouwen',
        'Kast in elkaar zetten',
        'Bureau assembleren',
        'Overige montage'
      ]
    },
    onderhoud: {
      name: 'Onderhoud & Reparatie',
      icon: '🛠️',
      tarief: '€38-48/uur',
      subspecialiteiten: [
        'Preventief onderhoud',
        'Deur repareren',
        'Raam repareren',
        'Hang- en sluitwerk',
        'Dakgoot reiniging',
        'Kozijn onderhoud',
        'Winterklaar maken',
        'Kleine reparaties',
        'Onderhoud tuin',
        'Overig onderhoud'
      ]
    },
    tegels: {
      name: 'Tegels & Vloeren',
      icon: '🏠',
      tarief: '€42-52/uur',
      subspecialiteiten: [
        'Badkamertegels leggen',
        'Keukentegels leggen',
        'Laminaat leggen',
        'PVC vloer leggen',
        'Plinten plaatsen',
        'Tegels repareren',
        'Vloerverwarming',
        'Parket leggen',
        'Tegels verwijderen',
        'Overige tegelwerk'
      ]
    },
    tuinonderhoud: {
      name: 'Tuinonderhoud',
      icon: '🌱',
      tarief: '€30-40/uur',
      subspecialiteiten: [
        'Gazon maaien',
        'Hagen snoeien',
        'Onkruid verwijderen',
        'Planten planten',
        'Tuinontwerp',
        'Seizoensonderhoud',
        'Bestrating leggen',
        'Schutting plaatsen',
        'Vijver aanleggen',
        'Overig tuinwerk'
      ]
    },
    verhuizing: {
      name: 'Verhuizing & Transport',
      icon: '🚚',
      tarief: '€65-85/uur',
      subspecialiteiten: [
        'Volledige verhuizing',
        'Inpakservice',
        'Meubeltransport',
        'Studentenverhuizing',
        'Bedrijfsverhuizing',
        'Opslag service',
        'Montage/demontage',
        'Piano verhuizing',
        'Internationale verhuizing',
        'Overig transport'
      ]
    },
    schoonmaak: {
      name: 'Schoonmaak Diensten',
      icon: '🧽',
      tarief: '€25-35/uur',
      subspecialiteiten: [
        'Huishoudelijke schoonmaak',
        'Kantoorschoonmaak',
        'Eindschoonmaak',
        'Ramen wassen',
        'Tapijt reinigen',
        'Periodieke schoonmaak',
        'Grote schoonmaak',
        'Na-renovatie schoonmaak',
        'Tuinmeubilair schoonmaken',
        'Overige schoonmaak'
      ]
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = () => {
    if (!formData.dienst || !formData.subspecialiteit) {
      toast({
        title: "Selecteer een dienst en subspecialiteit",
        variant: "destructive"
      });
      return;
    }

    if (!formData.voornaam || !formData.achternaam || !formData.email || !formData.telefoon || !formData.beschrijving) {
      toast({
        title: "Vul alle verplichte velden in",
        variant: "destructive"
      });
      return;
    }

    if (!formData.akkoord) {
      toast({
        title: "Ga akkoord met de algemene voorwaarden",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "Klus succesvol geplaatst!",
      description: "We nemen binnen 24 uur contact met u op."
    });

    // Reset form
    setFormData({
      dienst: '',
      subspecialiteit: '',
      voornaam: '',
      achternaam: '',
      email: '',
      telefoon: '',
      straat: '',
      huisnummer: '',
      postcode: '',
      stad: '',
      beschrijving: '',
      urgentie: 'normaal',
      voorkeuren: '',
      fotos: [],
      akkoord: false
    });
    setCurrentStep(1);
  };

  const nextStep = () => {
    if (currentStep === 1 && (!formData.dienst || !formData.subspecialiteit)) {
      toast({
        title: "Selecteer een dienst en subspecialiteit",
        variant: "destructive"
      });
      return;
    }
    setCurrentStep(prev => Math.min(prev + 1, 5));
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      <div className="pt-20 pb-16">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12 animate-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Plaats uw <span className="text-primary-600">klus</span>
            </h1>
            <p className="text-xl text-gray-600">
              Beschrijf uw klus in een paar stappen en ontvang snel een vakman
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto mt-8">
              <div className="flex items-center justify-center space-x-3 p-3 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-200">
                <Clock className="w-6 h-6 text-primary-600" />
                <span className="font-medium text-gray-900">Binnen 24u reactie</span>
              </div>
              <div className="flex items-center justify-center space-x-3 p-3 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-200">
                <Shield className="w-6 h-6 text-green-600" />
                <span className="font-medium text-gray-900">Ervaren vakmannen</span>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-center space-x-2 md:space-x-4">
              {[1, 2, 3, 4, 5].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                    step <= currentStep 
                      ? 'bg-primary-600 text-white' 
                      : 'bg-gray-200 text-gray-500'
                  }`}>
                    {step < currentStep ? <Check size={16} /> : step}
                  </div>
                  {step < 5 && (
                    <div className={`w-4 md:w-8 h-1 mx-1 md:mx-2 transition-all duration-300 ${
                      step < currentStep ? 'bg-primary-600' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-center mt-4 space-x-2 md:space-x-8 text-xs md:text-sm text-gray-600">
              <span className={currentStep === 1 ? 'font-semibold text-primary-600' : ''}>Dienst</span>
              <span className={currentStep === 2 ? 'font-semibold text-primary-600' : ''}>Gegevens</span>
              <span className={currentStep === 3 ? 'font-semibold text-primary-600' : ''}>Adres</span>
              <span className={currentStep === 4 ? 'font-semibold text-primary-600' : ''}>Details</span>
              <span className={currentStep === 5 ? 'font-semibold text-primary-600' : ''}>Afronden</span>
            </div>
          </div>

          {/* Form Content */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
            <CardContent className="p-6 md:p-8">
              {/* Step 1: Dienst & Subspecialiteit */}
              {currentStep === 1 && (
                <div className="animate-fade-in space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900">Welke dienst heeft u nodig?</h2>
                  
                  {/* Dienst Selectie */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.entries(diensten).map(([key, dienst]) => (
                      <div
                        key={key}
                        onClick={() => handleInputChange('dienst', key)}
                        className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 text-center ${
                          formData.dienst === key
                            ? 'border-primary-600 bg-primary-50'
                            : 'border-gray-200 hover:border-primary-300 hover:bg-gray-50'
                        }`}
                      >
                        <div className="text-3xl mb-2">{dienst.icon}</div>
                        <div className="text-sm font-medium text-gray-900 mb-1">{dienst.name}</div>
                        <div className="text-xs text-gray-600">{dienst.tarief}</div>
                        {formData.dienst === key && (
                          <Check className="w-5 h-5 text-primary-600 mx-auto mt-2" />
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Subspecialiteit Selectie */}
                  {formData.dienst && (
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold text-gray-900">
                        Wat voor {diensten[formData.dienst].name.toLowerCase()} heeft u nodig?
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {diensten[formData.dienst].subspecialiteiten.map((sub) => (
                          <div
                            key={sub}
                            onClick={() => handleInputChange('subspecialiteit', sub)}
                            className={`p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                              formData.subspecialiteit === sub
                                ? 'border-primary-600 bg-primary-50'
                                : 'border-gray-200 hover:border-primary-300 hover:bg-gray-50'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-900">{sub}</span>
                              {formData.subspecialiteit === sub && (
                                <Check className="w-4 h-4 text-primary-600" />
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Step 2: Persoonlijke Gegevens */}
              {currentStep === 2 && (
                <div className="animate-fade-in">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Uw contactgegevens</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="voornaam">Voornaam *</Label>
                      <Input
                        id="voornaam"
                        value={formData.voornaam}
                        onChange={(e) => handleInputChange('voornaam', e.target.value)}
                        placeholder="Uw voornaam"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="achternaam">Achternaam *</Label>
                      <Input
                        id="achternaam"
                        value={formData.achternaam}
                        onChange={(e) => handleInputChange('achternaam', e.target.value)}
                        placeholder="Uw achternaam"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">E-mailadres *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="<EMAIL>"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="telefoon">Telefoonnummer *</Label>
                      <Input
                        id="telefoon"
                        value={formData.telefoon}
                        onChange={(e) => handleInputChange('telefoon', e.target.value)}
                        placeholder="06-12345678"
                        className="mt-1"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Adresgegevens */}
              {currentStep === 3 && (
                <div className="animate-fade-in">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Adres van de klus</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="md:col-span-2">
                      <Label htmlFor="straat">Straatnaam</Label>
                      <Input
                        id="straat"
                        value={formData.straat}
                        onChange={(e) => handleInputChange('straat', e.target.value)}
                        placeholder="Hoofdstraat"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="huisnummer">Huisnummer</Label>
                      <Input
                        id="huisnummer"
                        value={formData.huisnummer}
                        onChange={(e) => handleInputChange('huisnummer', e.target.value)}
                        placeholder="123"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="postcode">Postcode</Label>
                      <Input
                        id="postcode"
                        value={formData.postcode}
                        onChange={(e) => handleInputChange('postcode', e.target.value)}
                        placeholder="1234 AB"
                        className="mt-1"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="stad">Stad</Label>
                      <Input
                        id="stad"
                        value={formData.stad}
                        onChange={(e) => handleInputChange('stad', e.target.value)}
                        placeholder="Amsterdam"
                        className="mt-1"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Step 4: Beschrijving & Details */}
              {currentStep === 4 && (
                <div className="animate-fade-in space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900">Beschrijf uw klus</h2>
                  
                  <div>
                    <Label htmlFor="beschrijving">Gedetailleerde beschrijving *</Label>
                    <Textarea
                      id="beschrijving"
                      value={formData.beschrijving}
                      onChange={(e) => handleInputChange('beschrijving', e.target.value)}
                      placeholder="Beschrijf zo gedetailleerd mogelijk wat u gedaan wilt hebben. Denk aan: wat, waar, wanneer, materialen, etc."
                      className="mt-1 min-h-32"
                      rows={6}
                    />
                  </div>

                  <div>
                    <Label className="text-lg font-semibold mb-2 block">Urgentie</Label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      {[
                        { value: 'normaal', label: 'Normaal', desc: 'Binnen 1-2 weken' },
                        { value: 'urgent', label: 'Urgent', desc: 'Binnen 1 week' },
                        { value: 'spoed', label: 'Spoed', desc: 'Zo snel mogelijk' }
                      ].map((option) => (
                        <div
                          key={option.value}
                          onClick={() => handleInputChange('urgentie', option.value)}
                          className={`p-3 border-2 rounded-lg cursor-pointer text-center transition-all ${
                            formData.urgentie === option.value
                              ? 'border-primary-600 bg-primary-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="font-medium">{option.label}</div>
                          <div className="text-sm text-gray-600">{option.desc}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="voorkeuren">Bijzondere wensen of voorkeuren</Label>
                    <Textarea
                      id="voorkeuren"
                      value={formData.voorkeuren}
                      onChange={(e) => handleInputChange('voorkeuren', e.target.value)}
                      placeholder="Bijvoorbeeld: gewenste uitvoering datum, materiaalvoorkeur, etc."
                      className="mt-1"
                      rows={3}
                    />
                  </div>

                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors duration-200">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">Sleep foto's hierheen of klik om te uploaden</p>
                    <p className="text-sm text-gray-500">Maximaal 5 foto's, JPG of PNG (optioneel)</p>
                    <Input type="file" multiple accept="image/*" className="hidden" />
                  </div>
                </div>
              )}

              {/* Step 5: Afronden */}
              {currentStep === 5 && (
                <div className="animate-fade-in space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900">Controleer uw gegevens</h2>
                  
                  <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                    <div>
                      <h3 className="font-semibold text-gray-900">Geselecteerde dienst:</h3>
                      <p className="text-gray-700">{diensten[formData.dienst]?.name} - {formData.subspecialiteit}</p>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Contactpersoon:</h3>
                      <p className="text-gray-700">{formData.voornaam} {formData.achternaam}</p>
                      <p className="text-gray-700">{formData.email} | {formData.telefoon}</p>
                    </div>
                    {formData.straat && (
                      <div>
                        <h3 className="font-semibold text-gray-900">Adres:</h3>
                        <p className="text-gray-700">
                          {formData.straat} {formData.huisnummer}, {formData.postcode} {formData.stad}
                        </p>
                      </div>
                    )}
                    <div>
                      <h3 className="font-semibold text-gray-900">Urgentie:</h3>
                      <p className="text-gray-700 capitalize">{formData.urgentie}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="akkoord"
                      checked={formData.akkoord}
                      onCheckedChange={(checked) => handleInputChange('akkoord', checked)}
                    />
                    <Label htmlFor="akkoord" className="text-sm">
                      Ik ga akkoord met de{' '}
                      <a href="/algemene-voorwaarden" className="text-primary-600 hover:underline">
                        algemene voorwaarden
                      </a>{' '}
                      en het verwerken van mijn gegevens. *
                    </Label>
                  </div>

                  <Alert>
                    <Shield className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Wat gebeurt er nu?</strong><br />
                      Na het versturen van uw klus nemen wij binnen 24 uur contact met u op voor een afspraak. 
                      U ontvangt een bevestiging per e-mail met alle details.
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
                {currentStep > 1 && (
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    className="px-6 py-2"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Vorige
                  </Button>
                )}
                <div className="ml-auto">
                  {currentStep < 5 ? (
                    <Button
                      onClick={nextStep}
                      className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2"
                    >
                      Volgende
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  ) : (
                    <Button
                      onClick={handleSubmit}
                      className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-3 text-lg font-semibold"
                    >
                      Klus Plaatsen
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default KlusPlaatsen;
