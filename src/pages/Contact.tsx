
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { toast } from '@/hooks/use-toast';

const Contact = () => {
  const [formData, setFormData] = useState({
    naam: '',
    email: '',
    telefoon: '',
    onderwerp: '',
    bericht: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!formData.naam || !formData.email || !formData.bericht) {
      toast({
        title: "Vul alle verplichte velden in",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "Bericht verzonden!",
      description: "We nemen zo snel mogelijk contact met u op."
    });

    setFormData({
      naam: '',
      email: '',
      telefoon: '',
      onderwerp: '',
      bericht: ''
    });
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      <div className="pt-20 pb-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12 animate-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              <span className="text-primary-600">Contact</span> opnemen
            </h1>
            <p className="text-xl text-gray-600">
              Heeft u vragen of wilt u een afspraak maken? We helpen u graag verder!
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Stuur ons een bericht</h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="naam">Naam *</Label>
                      <Input
                        id="naam"
                        value={formData.naam}
                        onChange={(e) => handleChange('naam', e.target.value)}
                        placeholder="Uw volledige naam"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="telefoon">Telefoonnummer</Label>
                      <Input
                        id="telefoon"
                        value={formData.telefoon}
                        onChange={(e) => handleChange('telefoon', e.target.value)}
                        placeholder="06-12345678"
                        className="mt-1"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="email">E-mailadres *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="onderwerp">Onderwerp</Label>
                    <Input
                      id="onderwerp"
                      value={formData.onderwerp}
                      onChange={(e) => handleChange('onderwerp', e.target.value)}
                      placeholder="Waar gaat uw bericht over?"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="bericht">Bericht *</Label>
                    <Textarea
                      id="bericht"
                      value={formData.bericht}
                      onChange={(e) => handleChange('bericht', e.target.value)}
                      placeholder="Typ hier uw bericht..."
                      className="mt-1 min-h-32"
                      rows={5}
                    />
                  </div>

                  <Button 
                    type="submit"
                    className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3"
                  >
                    Bericht verzenden
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <div className="space-y-8">
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Contactgegevens</h2>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mt-1">
                        <span className="text-primary-600 text-sm">📞</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">Telefoon</h3>
                        <p className="text-gray-600">06-12345678</p>
                        <p className="text-sm text-gray-500">Ma-vr: 8:00-18:00, za: 9:00-15:00</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mt-1">
                        <span className="text-primary-600 text-sm">📧</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">E-mail</h3>
                        <p className="text-gray-600"><EMAIL></p>
                        <p className="text-sm text-gray-500">We reageren binnen 24 uur</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mt-1">
                        <span className="text-primary-600 text-sm">📍</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">Werkgebied</h3>
                        <p className="text-gray-600">Nederland</p>
                        <p className="text-sm text-gray-500">Lokaal beschikbaar voor spoedklussen</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mt-1">
                        <span className="text-primary-600 text-sm">⏰</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">Openingstijden</h3>
                        <div className="text-gray-600 space-y-1">
                          <p>Maandag - Vrijdag: 8:00 - 18:00</p>
                          <p>Zaterdag: 9:00 - 15:00</p>
                          <p>Zondag: Op afspraak</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-r from-primary-600 to-primary-800 text-white border-0 shadow-xl">
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-4">Spoedeisende klussen?</h2>
                  <p className="text-primary-100 mb-6">
                    Voor urgente reparaties zijn we 24/7 bereikbaar voor noodgevallen.
                  </p>
                  <Button className="bg-accent-500 hover:bg-accent-600 text-white">
                    Noodlijn: 06-12345678
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Contact;
