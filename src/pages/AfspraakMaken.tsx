
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { toast } from '@/hooks/use-toast';
import { Calendar, Clock, Check, Users } from 'lucide-react';

const AfspraakMaken = () => {
  const [selectedService, setSelectedService] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [customerData, setCustomerData] = useState({
    naam: '',
    email: '',
    telefoon: '',
    adres: ''
  });

  const services = [
    { id: 'loodgieterswerk', name: 'Loodgieterswerk' },
    { id: 'elektriciteit', name: 'Elektriciteit' },
    { id: 'schilderwerk', name: 'Schilderwerk' },
    { id: 'montage', name: 'Montage & Assemblage' },
    { id: 'onderhoud', name: 'Onderhoud & Reparatie' },
    { id: 'tegels', name: 'Tegels & Vloeren' },
    { id: 'overig', name: 'Overige diensten' }
  ];

  // Generate next 14 days for appointment booking
  const generateAvailableDates = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 1; i <= 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      // Skip Sundays (day 0)
      if (date.getDay() !== 0) {
        dates.push({
          value: date.toISOString().split('T')[0],
          label: date.toLocaleDateString('nl-NL', { 
            weekday: 'long', 
            day: 'numeric', 
            month: 'long' 
          })
        });
      }
    }
    return dates;
  };

  const timeSlots = [
    '08:00', '09:00', '10:00', '11:00', '12:00',
    '13:00', '14:00', '15:00', '16:00', '17:00'
  ];

  const availableDates = generateAvailableDates();

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!selectedService || !selectedDate || !selectedTime || !customerData.naam || !customerData.email || !customerData.telefoon) {
      toast({
        title: "Vul alle verplichte velden in",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "Afspraak succesvol ingepland!",
      description: `Uw afspraak is gepland voor ${availableDates.find(d => d.value === selectedDate)?.label} om ${selectedTime}. U ontvangt een bevestiging per e-mail.`
    });

    // Reset form
    setSelectedService('');
    setSelectedDate('');
    setSelectedTime('');
    setCustomerData({
      naam: '',
      email: '',
      telefoon: '',
      adres: ''
    });
  };

  const handleCustomerDataChange = (field, value) => {
    setCustomerData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      <div className="pt-20 pb-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12 animate-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              <span className="text-primary-600">Afspraak</span> maken
            </h1>
            <p className="text-xl text-gray-600">
              Plan direct een afspraak met onze vakmannen. Snel en eenvoudig online.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Booking Form */}
            <div className="lg:col-span-2">
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold text-gray-900">Plan uw afspraak</CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Service Selection */}
                    <div>
                      <Label className="text-base font-semibold">Welke dienst heeft u nodig? *</Label>
                      <Select value={selectedService} onValueChange={setSelectedService}>
                        <SelectTrigger className="mt-2">
                          <SelectValue placeholder="Selecteer een dienst" />
                        </SelectTrigger>
                        <SelectContent className="bg-white">
                          {services.map((service) => (
                            <SelectItem key={service.id} value={service.id}>
                              {service.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Date Selection */}
                    <div>
                      <Label className="text-base font-semibold">Kies een datum *</Label>
                      <Select value={selectedDate} onValueChange={setSelectedDate}>
                        <SelectTrigger className="mt-2">
                          <SelectValue placeholder="Selecteer een datum" />
                        </SelectTrigger>
                        <SelectContent className="bg-white">
                          {availableDates.map((date) => (
                            <SelectItem key={date.value} value={date.value}>
                              {date.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Time Selection */}
                    {selectedDate && (
                      <div className="animate-fade-in">
                        <Label className="text-base font-semibold">Kies een tijdstip *</Label>
                        <div className="grid grid-cols-3 md:grid-cols-5 gap-2 mt-2">
                          {timeSlots.map((time) => (
                            <button
                              key={time}
                              type="button"
                              onClick={() => setSelectedTime(time)}
                              className={`p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 ${
                                selectedTime === time
                                  ? 'border-primary-600 bg-primary-50 text-primary-600'
                                  : 'border-gray-200 hover:border-primary-300 hover:bg-gray-50'
                              }`}
                            >
                              {time}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Customer Information */}
                    {selectedTime && (
                      <div className="animate-fade-in space-y-4">
                        <h3 className="text-lg font-semibold text-gray-900">Uw gegevens</h3>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="naam">Naam *</Label>
                            <Input
                              id="naam"
                              value={customerData.naam}
                              onChange={(e) => handleCustomerDataChange('naam', e.target.value)}
                              placeholder="Uw volledige naam"
                              className="mt-1"
                            />
                          </div>
                          <div>
                            <Label htmlFor="telefoon">Telefoonnummer *</Label>
                            <Input
                              id="telefoon"
                              value={customerData.telefoon}
                              onChange={(e) => handleCustomerDataChange('telefoon', e.target.value)}
                              placeholder="06-12345678"
                              className="mt-1"
                            />
                          </div>
                        </div>

                        <div>
                          <Label htmlFor="email">E-mailadres *</Label>
                          <Input
                            id="email"
                            type="email"
                            value={customerData.email}
                            onChange={(e) => handleCustomerDataChange('email', e.target.value)}
                            placeholder="<EMAIL>"
                            className="mt-1"
                          />
                        </div>

                        <div>
                          <Label htmlFor="adres">Adres van de afspraak</Label>
                          <Input
                            id="adres"
                            value={customerData.adres}
                            onChange={(e) => handleCustomerDataChange('adres', e.target.value)}
                            placeholder="Straat + huisnummer, plaats"
                            className="mt-1"
                          />
                        </div>

                        <Button 
                          type="submit"
                          className="w-full bg-accent-500 hover:bg-accent-600 text-white py-3 text-lg"
                        >
                          Afspraak bevestigen
                        </Button>
                      </div>
                    )}
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Summary & Info */}
            <div className="space-y-6">
              {/* Appointment Summary */}
              {(selectedService || selectedDate || selectedTime) && (
                <Card className="bg-primary-50/50 backdrop-blur-sm border-primary-200">
                  <CardHeader>
                    <CardTitle className="text-lg text-primary-800">Uw afspraak</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedService && (
                        <div className="flex items-center space-x-2">
                          <Users className="w-4 h-4 text-primary-600" />
                          <span className="text-sm font-medium">
                            {services.find(s => s.id === selectedService)?.name}
                          </span>
                        </div>
                      )}
                      {selectedDate && (
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4 text-primary-600" />
                          <span className="text-sm">
                            {availableDates.find(d => d.value === selectedDate)?.label}
                          </span>
                        </div>
                      )}
                      {selectedTime && (
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-primary-600" />
                          <span className="text-sm">{selectedTime}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Info Card */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-gray-900 mb-4">Goed om te weten</h3>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-start space-x-2">
                      <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>Gratis advies ter plaatse</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>Professioneel gereedschap</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>Opruimen na afloop</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>Garantie op uitgevoerd werk</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Contact Card */}
              <Card className="bg-gradient-to-r from-secondary-600 to-secondary-800 text-white border-0">
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-3">Vragen over uw afspraak?</h3>
                  <p className="text-secondary-100 text-sm mb-4">
                    Bel ons gerust voor meer informatie of wijzigingen.
                  </p>
                  <p className="font-semibold">📞 06-12345678</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AfspraakMaken;
