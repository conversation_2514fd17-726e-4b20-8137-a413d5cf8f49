
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Projecten = () => {
  const projects = [
    {
      title: "Badkamer Renovatie",
      category: "Loodgieterswerk & Tegels",
      description: "Complete badkamerrenovatie inclusief nieuwe tegels, sanitair en verlichting.",
      image: "https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?w=600&h=400&fit=crop",
      duration: "5 dagen",
      location: "Amsterdam"
    },
    {
      title: "Keuken Installatie",
      category: "Montage & Elektriciteit", 
      description: "Volledige IKEA keuken gemonteerd met nieuwe elektrische aansluitingen.",
      image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop",
      duration: "3 dagen",
      location: "Utrecht"
    },
    {
      title: "Woonkamer Schilderwerk",
      category: "Schilderwerk",
      description: "Professioneel schilderwerk van wanden en plafond in moderne kleuren.",
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop",
      duration: "2 dagen",
      location: "Rotterdam"
    },
    {
      title: "Elektra Uitbreiding",
      category: "Elektriciteit",
      description: "Groepenkast uitgebreid en nieuwe stopcontacten geïnstalleerd.",
      image: "https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=600&h=400&fit=crop",
      duration: "1 dag",
      location: "Den Haag"
    },
    {
      title: "Tuin Renovatie",
      category: "Tuinonderhoud",
      description: "Complete tuin opnieuw aangelegd met nieuwe beplanting en bestrating.",
      image: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=600&h=400&fit=crop",
      duration: "1 week",
      location: "Eindhoven"
    },
    {
      title: "Kantoor Verbouwing",
      category: "Montage & Schilderwerk",
      description: "Kantoorruimte verbouwd met nieuwe wandindeling en professionele afwerking.",
      image: "https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop",
      duration: "1 week",
      location: "Groningen"
    }
  ];

  const categories = ["Alle projecten", "Loodgieterswerk", "Elektriciteit", "Schilderwerk", "Montage", "Tuinonderhoud"];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      <div className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16 animate-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Onze <span className="text-primary-600">Projecten</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Bekijk een selectie van onze afgeronde projecten. Van kleine reparaties tot grote verbouwingen.
            </p>
          </div>

          {/* Filter Buttons */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category, index) => (
              <Button
                key={index}
                variant={index === 0 ? "default" : "outline"}
                className={`px-6 py-2 rounded-full transition-all duration-200 ${
                  index === 0 
                    ? 'bg-primary-600 hover:bg-primary-700 text-white' 
                    : 'border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white'
                }`}
              >
                {category}
              </Button>
            ))}
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {projects.map((project, index) => (
              <Card key={index} className="overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                <div className="relative">
                  <img 
                    src={project.image}
                    alt={project.title}
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {project.category}
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <p className="text-sm opacity-90">📍 {project.location}</p>
                    <p className="text-sm opacity-90">⏱️ {project.duration}</p>
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{project.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{project.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Success Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            <Card className="text-center p-6 bg-white/70 backdrop-blur-sm border-0 shadow-lg">
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-primary-600 mb-2">1000+</div>
                <div className="text-gray-600">Projecten voltooid</div>
              </CardContent>
            </Card>
            <Card className="text-center p-6 bg-white/70 backdrop-blur-sm border-0 shadow-lg">
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-green-600 mb-2">98%</div>
                <div className="text-gray-600">Tevredenheid</div>
              </CardContent>
            </Card>
            <Card className="text-center p-6 bg-white/70 backdrop-blur-sm border-0 shadow-lg">
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-blue-600 mb-2">5 jaar</div>
                <div className="text-gray-600">Gemiddelde garantie</div>
              </CardContent>
            </Card>
            <Card className="text-center p-6 bg-white/70 backdrop-blur-sm border-0 shadow-lg">
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-purple-600 mb-2">24u</div>
                <div className="text-gray-600">Responstijd</div>
              </CardContent>
            </Card>
          </div>

          {/* CTA Section */}
          <Card className="bg-gradient-to-r from-primary-600 to-primary-800 text-white border-0 shadow-xl">
            <CardContent className="p-12 text-center">
              <h2 className="text-3xl font-bold mb-4">Klaar voor uw eigen project?</h2>
              <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
                Laat ons ook uw droomproject realiseren. Van concept tot oplevering begeleiden wij u graag.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/klus-plaatsen">
                  <Button className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-3 text-lg">
                    Start uw project
                  </Button>
                </Link>
                <Link to="/contact">
                  <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600 px-8 py-3 text-lg">
                    Vrijblijvend advies
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Projecten;
