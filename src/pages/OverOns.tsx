
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Check, Users, Clock, Home } from 'lucide-react';

const OverOns = () => {
  const values = [
    {
      icon: Check,
      title: "Kwaliteit",
      description: "Vakmanschap staat bij ons centraal. Elk project wordt met de grootste zorg uitgevoerd."
    },
    {
      icon: Clock,
      title: "Betrouwbaarheid", 
      description: "Wij komen onze afspraken na. Op tijd, binnen budget en volgens verwachting."
    },
    {
      icon: Users,
      title: "Service",
      description: "Persoonlijke aandacht en maatwerk. Uw tevredenheid is onze prioriteit."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      <div className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16 animate-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Over <span className="text-primary-600">ASklussen.nl</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Uw betrouwbare partner voor alle klussen in en om het huis. 
              Met jaren ervaring en passie voor vakmanschap.
            </p>
          </div>

          {/* Story Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-gray-900">Ons Verhaal</h2>
              <p className="text-gray-600 leading-relaxed">
                ASklussen.nl is ontstaan uit de passie om mensen te helpen met alle klussen 
                in en rond het huis. Wat begon als een kleine onderneming is uitgegroeid 
                tot een betrouwbare partner voor duizenden tevreden klanten.
              </p>
              <p className="text-gray-600 leading-relaxed">
                Onze ervaren vakmannen hebben jarenlange expertise in verschillende 
                vakgebieden. Van kleine reparaties tot grote verbouwprojecten - 
                wij pakken elke klus met dezelfde toewijding aan.
              </p>
              <p className="text-gray-600 leading-relaxed">
                We geloven in eerlijkheid, transparantie en vakmanschap. 
                Deze waarden vormen de basis van alles wat we doen.
              </p>
            </div>
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop"
                alt="Het team van ASklussen.nl"
                className="w-full h-96 object-cover rounded-2xl shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
            </div>
          </div>

          {/* Values */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Onze Kernwaarden</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {values.map((value, index) => (
                <Card key={index} className="text-center p-8 bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                  <CardContent className="pt-6">
                    <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <value.icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">{value.title}</h3>
                    <p className="text-gray-600">{value.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Mission */}
          <Card className="bg-gradient-to-r from-primary-600 to-primary-800 text-white border-0 shadow-xl mb-16">
            <CardContent className="p-12 text-center">
              <h2 className="text-3xl font-bold mb-6">Onze Missie</h2>
              <p className="text-xl text-primary-100 max-w-4xl mx-auto leading-relaxed">
                Wij maken het leven van onze klanten makkelijker door hoogwaardige diensten 
                te leveren met een persoonlijke touch. Van het eerste contact tot de 
                oplevering van het project staan service en kwaliteit centraal.
              </p>
            </CardContent>
          </Card>

          {/* Why Choose Us */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Waarom ASklussen.nl?</h3>
                <ul className="space-y-4">
                  <li className="flex items-start space-x-3">
                    <Check className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span className="text-gray-700">Jaren ervaring in alle vakgebieden</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <Check className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span className="text-gray-700">Transparante prijsstelling</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <Check className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span className="text-gray-700">Garantie op alle werkzaamheden</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <Check className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span className="text-gray-700">Flexibele planning en bereikbaarheid</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <Check className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span className="text-gray-700">Professioneel gereedschap en materialen</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Onze Expertise</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <p className="font-medium text-gray-900">🔧 Loodgieterswerk</p>
                    <p className="font-medium text-gray-900">⚡ Elektriciteit</p>
                    <p className="font-medium text-gray-900">🎨 Schilderwerk</p>
                    <p className="font-medium text-gray-900">🔨 Montage</p>
                  </div>
                  <div className="space-y-2">
                    <p className="font-medium text-gray-900">🛠️ Onderhoud</p>
                    <p className="font-medium text-gray-900">🏠 Tegels & Vloeren</p>
                    <p className="font-medium text-gray-900">🌱 Tuinonderhoud</p>
                    <p className="font-medium text-gray-900">❓ Maatwerk</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default OverOns;
