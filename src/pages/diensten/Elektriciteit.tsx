
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Link } from 'react-router-dom';
import { Check, Star, Clock, Euro, Phone, ArrowRight, Zap, Shield, AlertTriangle } from 'lucide-react';

const Elektriciteit = () => {
  const klussen = [
    {
      title: "Stopcontact Installatie",
      description: "Nieuwe stopcontacten professioneel geïnstalleerd",
      price: "vanaf €65",
      duration: "1-2 uur",
      urgency: "Normaal"
    },
    {
      title: "Verlichting Installeren",
      description: "Lampen en verlichtingsarmaturen installeren",
      price: "vanaf €55",
      duration: "1-3 uur", 
      urgency: "Normaal"
    },
    {
      title: "Groepenkast Uitbreiden",
      description: "Uitbreiding van uw elektrische installatie",
      price: "vanaf €125",
      duration: "2-4 uur",
      urgency: "Planning"
    },
    {
      title: "Storing Oplossen",
      description: "Elektrische storingen snel verholpen",
      price: "vanaf €75",
      duration: "1-2 uur",
      urgency: "Spoed mogelijk"
    },
    {
      title: "Periodieke Keuring",
      description: "Wettelijk verplichte elektrische keuring",
      price: "vanaf €150",
      duration: "2-3 uur",
      urgency: "Gepland"
    },
    {
      title: "Smart Home Installatie",
      description: "Moderne domotica systemen installeren",
      price: "op maat",
      duration: "1-2 dagen",
      urgency: "Planning"
    }
  ];

  const features = [
    "Gecertificeerde elektriciens",
    "Veiligheid staat voorop",
    "Keuringscertificaten afgegeven",
    "Smart home specialisten",
    "24/7 spoedservice bij stroomuitval",
    "Garantie op alle werkzaamheden"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-r from-yellow-500 to-orange-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-white animate-fade-in">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="text-3xl">⚡</span><br />
                Professionele Elektriciteit
              </h1>
              <p className="text-xl text-yellow-100 mb-8">
                Veilige en betrouwbare elektrische installaties door 
                gecertificeerde elektriciens. Van storingen tot smart home.
              </p>
              
              <div className="bg-red-500 p-4 rounded-lg mb-6 flex items-start space-x-3">
                <AlertTriangle className="w-6 h-6 text-white flex-shrink-0 mt-1" />
                <div>
                  <div className="font-bold">Belangrijke veiligheidswaarschuwing</div>
                  <div className="text-sm">Probeer nooit zelf elektrische klussen uit te voeren. Dit is levensgevaarlijk en wettelijk alleen toegestaan voor gecertificeerde elektriciens.</div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/klus-plaatsen">
                  <Button className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-3 text-lg font-semibold">
                    Elektricien Inschakelen
                  </Button>
                </Link>
                <a href="tel:06-12345678">
                  <Button variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 px-8 py-3 text-lg">
                    <Phone className="w-5 h-5 mr-2" />
                    Spoed: Stroomuitval
                  </Button>
                </a>
              </div>
            </div>
            
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=600&h=400&fit=crop"
                alt="Elektricien aan het werk"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-white p-6 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <Shield className="w-8 h-8 text-green-600" />
                  <div>
                    <div className="font-bold text-gray-900">100% Gecertificeerd</div>
                    <div className="text-sm text-gray-600">Veilig & volgens normen</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Onze Elektriciteit Diensten
            </h2>
            <p className="text-xl text-gray-600">
              Professionele elektrische installaties en reparaties door gecertificeerde specialisten
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {klussen.map((klus, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-900 flex items-center">
                    <Zap className="w-5 h-5 mr-2 text-yellow-500" />
                    {klus.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{klus.description}</p>
                  <div className="space-y-2 mb-6">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Prijs:</span>
                      <span className="font-semibold text-accent-600">{klus.price}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Duur:</span>
                      <span className="font-medium text-gray-900">{klus.duration}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Urgentie:</span>
                      <span className="text-sm text-primary-600">{klus.urgency}</span>
                    </div>
                  </div>
                  <Link to="/klus-plaatsen">
                    <Button className="w-full bg-yellow-500 hover:bg-yellow-600 text-white">
                      Aanvragen <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Waarom onze elektriciens?
              </h2>
              <div className="space-y-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <Check className="w-6 h-6 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-white p-8 rounded-lg shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Tarieven Elektriciteit</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center border-b pb-2">
                  <span>Uurtarief elektricien</span>
                  <span className="font-semibold">€50-60/uur</span>
                </div>
                <div className="flex justify-between items-center border-b pb-2">
                  <span>Spoedtoeslag (avond/weekend)</span>
                  <span className="font-semibold">+50%</span>
                </div>
                <div className="flex justify-between items-center border-b pb-2">
                  <span>Voorrijkosten</span>
                  <span className="font-semibold">€25</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Gratis offerte</span>
                  <span className="font-semibold text-green-600">€0</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-gradient-to-r from-yellow-500 to-orange-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Elektrische klus? Veiligheid voorop!
          </h2>
          <p className="text-xl text-yellow-100 mb-8">
            Laat het over aan de professionals
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/klus-plaatsen">
              <Button className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-3 text-lg font-semibold">
                Direct Elektricien
              </Button>
            </Link>
            <Link to="/offerte">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 px-8 py-3 text-lg">
                Gratis Offerte
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Elektriciteit;
