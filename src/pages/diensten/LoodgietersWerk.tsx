
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Link } from 'react-router-dom';
import { Check, Star, Clock, Euro, Phone, ArrowRight, Users, Shield } from 'lucide-react';

const LoodgietersWerk = () => {
  const klussen = [
    {
      title: "Lekkage Reparatie",
      description: "Snelle reparatie van leidinglekkages",
      price: "vanaf €75",
      duration: "1-2 uur",
      urgency: "Spoed mogelijk"
    },
    {
      title: "<PERSON>raan Installatie",
      description: "Installatie van nieuwe kranen en mengkranen",
      price: "vanaf €85",
      duration: "1-3 uur",
      urgency: "Normaal"
    },
    {
      title: "Toilet Reparatie",
      description: "Reparatie en vervanging van toiletten",
      price: "vanaf €95",
      duration: "2-4 uur",
      urgency: "Normaal"
    },
    {
      title: "Verstoppingen",
      description: "Verhelpen van verstoppingen in afvoeren",
      price: "vanaf €65",
      duration: "1-2 uur",
      urgency: "Urgent mogelijk"
    },
    {
      title: "CV-Ketel Onderhoud",
      description: "Onderhoud en reparatie van CV-ketels",
      price: "vanaf €125",
      duration: "2-3 uur",
      urgency: "Seizoensgebonden"
    },
    {
      title: "Badkamer Renovatie",
      description: "Complete badkamer loodgieterswerk",
      price: "op maat",
      duration: "1-3 dagen",
      urgency: "Planning"
    }
  ];

  const features = [
    "24/7 Spoedservice beschikbaar",
    "Ervaren en gecertificeerde loodgieters",
    "Transparante prijzen zonder verrassingen",
    "Garantie op alle werkzaamheden",
    "Gratis inspectie en advies",
    "Eigen materiaalvoorraad"
  ];

  const reviews = [
    {
      name: "Maria S.",
      rating: 5,
      comment: "Snelle service bij lekkage in de keuken. Zeer tevreden!",
      date: "2 weken geleden"
    },
    {
      name: "Piet J.",
      rating: 5,
      comment: "Professionele installatie van nieuwe kraan. Netjes uitgevoerd.",
      date: "1 maand geleden"
    },
    {
      name: "Lisa K.",
      rating: 4,
      comment: "Goed werk aan CV-ketel. Op tijd en binnen budget.",
      date: "3 weken geleden"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-r from-primary-600 to-primary-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-white animate-fade-in">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="text-3xl">🔧</span><br />
                Professioneel Loodgieterswerk
              </h1>
              <p className="text-xl text-primary-100 mb-8">
                Van spoedlekkages tot complete badkamerrenovaties. 
                Onze ervaren loodgieters staan 24/7 voor u klaar.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/klus-plaatsen">
                  <Button className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-3 text-lg">
                    Direct Loodgieter Inschakelen
                  </Button>
                </Link>
                <a href="tel:06-12345678">
                  <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600 px-8 py-3 text-lg">
                    <Phone className="w-5 h-5 mr-2" />
                    24/7 Spoedlijn
                  </Button>
                </a>
              </div>
              
              <div className="grid grid-cols-3 gap-6 mt-12">
                <div className="text-center">
                  <div className="text-3xl font-bold text-accent-400">24/7</div>
                  <div className="text-primary-100">Bereikbaar</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-accent-400">15+</div>
                  <div className="text-primary-100">Jaar ervaring</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-accent-400">4.8★</div>
                  <div className="text-primary-100">Klantbeoordeling</div>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop"
                alt="Professionele loodgieter aan het werk"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <Clock className="w-8 h-8 text-primary-600" />
                  <div>
                    <div className="font-bold text-gray-900">Binnen 2 uur ter plaatse</div>
                    <div className="text-sm text-gray-600">Bij spoedklussen</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Onze Loodgieters Diensten
            </h2>
            <p className="text-xl text-gray-600">
              Gespecialiseerd in alle vormen van loodgieterswerk voor particulieren en bedrijven
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {klussen.map((klus, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-900">{klus.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{klus.description}</p>
                  <div className="space-y-2 mb-6">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Prijs:</span>
                      <span className="font-semibold text-accent-600">{klus.price}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Duur:</span>
                      <span className="font-medium text-gray-900">{klus.duration}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Urgentie:</span>
                      <span className="text-sm text-primary-600">{klus.urgency}</span>
                    </div>
                  </div>
                  <Link to="/klus-plaatsen">
                    <Button className="w-full bg-primary-600 hover:bg-primary-700 text-white">
                      Aanvragen <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Waarom kiezen voor onze loodgieters?
              </h2>
              <div className="space-y-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <Check className="w-6 h-6 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
              <div className="mt-8 p-6 bg-primary-50 rounded-lg border border-primary-200">
                <h3 className="font-bold text-primary-800 mb-2">Spoedservice 24/7</h3>
                <p className="text-primary-700">
                  Heeft u een spoedlekkage of andere urgente loodgieterreparatie? 
                  Bel onze 24/7 spoedlijn: <strong>06-12345678</strong>
                </p>
              </div>
            </div>
            <div>
              <img 
                src="https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=600&h=400&fit=crop"
                alt="Loodgieter tools en materialen"
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Reviews */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Wat onze klanten zeggen
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {reviews.map((review, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="flex text-yellow-400">
                      {[...Array(review.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 fill-current" />
                      ))}
                    </div>
                    <span className="ml-2 text-sm text-gray-500">{review.date}</span>
                  </div>
                  <p className="text-gray-700 mb-4">"{review.comment}"</p>
                  <div className="font-semibold text-gray-900">- {review.name}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-accent-500 to-accent-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Heeft u een loodgieter nodig?
          </h2>
          <p className="text-xl text-accent-100 mb-8">
            Plaats uw klus of vraag een gratis offerte aan
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/klus-plaatsen">
              <Button className="bg-white text-accent-600 hover:bg-gray-100 px-8 py-3 text-lg font-semibold">
                Direct Klus Plaatsen
              </Button>
            </Link>
            <Link to="/offerte">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-accent-600 px-8 py-3 text-lg">
                Gratis Offerte
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default LoodgietersWerk;
