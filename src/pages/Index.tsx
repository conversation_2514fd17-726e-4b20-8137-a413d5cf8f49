
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Shield, Zap, Award, Star, Clock, Wrench, Hammer, Zap as Electric, Paintbrush, Home, Thermometer, Droplets, Wifi, Car, Leaf, Phone, Truck, Scissors, Bug, Monitor, Settings, Snowflake, Lightbulb, Drill, Screwdriver, Pickaxe, Building, Palette } from 'lucide-react';
import { useEffect, useState } from 'react';

const Index = () => {
  const [currentReviewIndex, setCurrentReviewIndex] = useState(0);

  const whyChooseUs = [
    {
      icon: Shield,
      title: "100% Betrouwbaar & Verzekerd",
      description: "Gecertificeerde vakmannen met jarenlange ervaring",
      details: [
        "Volledig verzekerd en gecertificeerd",
        "Garantie op alle uitgevoerde werkzaamheden",
        "Transparante werkwijze zonder verrassingen",
        "Vaste tarieven bekend vooraf"
      ],
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: Zap,
      title: "Supersnel & Flexibel",
      description: "Snelle service wanneer u het nodig heeft",
      details: [
        "Binnen 24 uur reactie op uw aanvraag",
        "Spoedklussen binnen 4 uur mogelijk",
        "Flexibele planning ook 's avonds/weekend",
        "Direct telefonisch bereikbaar"
      ],
      color: "from-orange-500 to-orange-600"
    }
  ];

  const qualityService = [
    {
      icon: Award,
      title: "Premium Kwaliteit",
      description: "Vakmanschap dat u kunt vertrouwen",
      details: [
        "15+ jaar ervaring in alle vakgebieden",
        "4.9★ gemiddelde klantbeoordeling",
        "Gebruik van hoogwaardige materialen",
        "Nazorg en ondersteuning na oplevering"
      ],
      color: "from-green-500 to-green-600"
    },
    {
      icon: Star,
      title: "Eerlijke Prijzen",
      description: "Kwaliteit hoeft niet duur te zijn",
      details: [
        "Scherpe en concurrerende tarieven",
        "Gratis offerte en advies vooraf",
        "Geen verborgen kosten of toeslagen",
        "Betaling pas na volledige tevredenheid"
      ],
      color: "from-purple-500 to-purple-600"
    }
  ];

  const services = [
    { title: "Loodgieter", icon: Droplets, description: "Leidingen, kranen en sanitair" },
    { title: "Elektricien", icon: Electric, description: "Elektrische installaties en reparaties" },
    { title: "Klusjesman", icon: Hammer, description: "Algemene klussen en reparaties" },
    { title: "CV monteur", icon: Thermometer, description: "Verwarming en klimaatbeheersing" },
    { title: "Witgoed reparatie", icon: Settings, description: "Wasmachine, droger en vaatwasser" },
    { title: "Dakdekker", icon: Home, description: "Dakbedekking en dakgoot onderhoud" },
    { title: "Smart Home", icon: Wifi, description: "Domotica en slimme installaties" },
    { title: "Timmerman", icon: Drill, description: "Houtwerk en meubelreparaties" },
    { title: "Schilder", icon: Paintbrush, description: "Binnen- en buitenschilderwerk" },
    { title: "Behanger", icon: Palette, description: "Behang en wandbekleding" },
    { title: "Stukadoor", icon: Screwdriver, description: "Pleisterwerk en afwerking" },
    { title: "Hovenier", icon: Leaf, description: "Tuin onderhoud en aanleg" },
    { title: "Koeltechniek", icon: Snowflake, description: "Koeling en airconditioning" },
    { title: "Slotenmaker", icon: Wrench, description: "Sloten en beveiligingssystemen" },
    { title: "Stratenmaker", icon: Pickaxe, description: "Bestrating en tuinpaden" },
    { title: "Ongediertebestrijder", icon: Bug, description: "Pest control en preventie" },
    { title: "ICT Specialist", icon: Monitor, description: "Computer en netwerk support" },
    { title: "Comfort Installateur", icon: Lightbulb, description: "Comfort en energiebesparende systemen" },
    { title: "Telefoonreparatie", icon: Phone, description: "Smartphone en tablet reparaties" },
    { title: "Isolatiebedrijf", icon: Building, description: "Isolatie en energiebesparing" },
    { title: "Klushulp", icon: Car, description: "Verhuizing en transport hulp" },
    { title: "Glaszetter", icon: Star, description: "Glas plaatsing en reparatie" }
  ];

  const reviews = [
    {
      name: "Maria van den Berg",
      text: "Uitstekend werk geleverd! Snel, netjes en voor een eerlijke prijs.",
      rating: 5
    },
    {
      name: "Jan Hendriks",
      text: "Zeer tevreden met de service. Punctueel en vakkundig.",
      rating: 5
    },
    {
      name: "Linda de Vries",
      text: "Aanrader! Professioneel en betrouwbaar.",
      rating: 5
    },
    {
      name: "Peter Jansen",
      text: "Fantastische service! Probleem snel opgelost en zeer vriendelijk.",
      rating: 5
    },
    {
      name: "Sandra Bakker",
      text: "Top kwaliteit werk. Precies wat ik nodig had. Zeker een aanrader!",
      rating: 5
    },
    {
      name: "Tom de Wit",
      text: "Snelle reactie en perfecte uitvoering. Heel tevreden!",
      rating: 5
    },
    {
      name: "Emma Visser",
      text: "Zeer professioneel en betrouwbaar. Komt afspraken na.",
      rating: 5
    },
    {
      name: "Mark Smit",
      text: "Uitstekende prijs-kwaliteit verhouding. Absoluut tevreden!",
      rating: 5
    }
  ];

  // Auto-scroll reviews carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentReviewIndex((prevIndex) =>
        prevIndex === reviews.length - 3 ? 0 : prevIndex + 1
      );
    }, 4000); // Change every 4 seconds

    return () => clearInterval(interval);
  }, [reviews.length]);

  // Get visible reviews (3 at a time)
  const getVisibleReviews = () => {
    const visibleReviews = [];
    for (let i = 0; i < 3; i++) {
      const index = (currentReviewIndex + i) % reviews.length;
      visibleReviews.push(reviews[index]);
    }
    return visibleReviews;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-accent-500/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="animate-fade-in">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Uw klus,
                <span className="text-primary-600"> onze expertise</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Van kleine reparaties tot grote verbouwingen - wij pakken elke klus 
                professioneel aan. Betrouwbaar, snel en tegen een eerlijke prijs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/klus-plaatsen">
                  <Button className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                    Wat is uw klus?
                  </Button>
                </Link>
                <Link to="/afspraak-maken">
                  <Button variant="outline" className="border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                    Direct een afspraak
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative animate-float">
              <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-8 border border-white/30">
                <img
                  src="/Handyman (1).png"
                  alt="Professionele klusman met gereedschap - meerdere armen met verschillende tools"
                  className="w-full h-80 object-cover rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-50">
          <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 to-blue-100/30"></div>
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }}></div>
        </div>

        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
              ⚡ Waarom 10.000+ klanten voor ons kiezen
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              De <span className="bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">slimste keuze</span> voor uw klus
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Ontdek waarom wij de nummer 1 klusservice zijn in de regio
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {whyChooseUs.map((feature, index) => (
              <Card key={index} className="group relative overflow-hidden bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                {/* Glassmorphism effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>

                <CardContent className="p-6 relative">
                  <div className="flex items-start space-x-4 mb-5">
                    <div className={`relative w-14 h-14 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                      <feature.icon className="w-7 h-7 text-white" />
                      {/* Glow effect */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} rounded-2xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-300`}></div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {feature.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="flex items-center text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-300">
                        <div className="w-2 h-2 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full mr-3 flex-shrink-0 group-hover:scale-125 transition-transform duration-300"></div>
                        <span className="font-medium">{detail}</span>
                      </div>
                    ))}
                  </div>

                  {/* Animated border */}
                  <div className={`absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r ${feature.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out`}></div>

                  {/* Corner accent */}
                  <div className="absolute top-4 right-4 w-8 h-8 bg-gradient-to-br from-primary-100 to-blue-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Quality & Service Section */}
      <section className="py-16 bg-gradient-to-br from-white via-gray-50/50 to-slate-50 relative">
        {/* Floating elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-gradient-to-br from-primary-200/30 to-blue-200/30 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-gradient-to-br from-accent-200/30 to-orange-200/30 rounded-full blur-xl"></div>

        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 rounded-full text-sm font-medium mb-4">
              🏆 Premium kwaliteit & service
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">Vakmanschap</span> & Eerlijke Prijzen
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Kwaliteit hoeft niet duur te zijn. Ontdek onze unieke aanpak.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {qualityService.map((feature, index) => (
              <Card key={index} className="group relative overflow-hidden bg-white/90 backdrop-blur-sm border border-gray-100 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-transparent"></div>

                <CardContent className="p-6 relative">
                  <div className="flex items-start space-x-4 mb-5">
                    <div className={`relative w-14 h-14 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                      <feature.icon className="w-7 h-7 text-white" />
                      {/* Pulse effect */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} rounded-2xl animate-pulse opacity-20`}></div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-green-600 transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {feature.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="flex items-center text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-300">
                        <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mr-3 flex-shrink-0 group-hover:scale-125 transition-transform duration-300"></div>
                        <span className="font-medium">{detail}</span>
                      </div>
                    ))}
                  </div>

                  {/* Animated border */}
                  <div className={`absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r ${feature.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out`}></div>

                  {/* Success indicator */}
                  <div className="absolute top-4 right-4 w-6 h-6 bg-gradient-to-br from-green-400 to-emerald-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Customer Reviews Section */}
      <section className="py-16 bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 relative">
        {/* Subtle background elements */}
        <div className="absolute top-10 right-10 w-20 h-20 bg-gradient-to-br from-primary-200/20 to-blue-200/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 left-10 w-32 h-32 bg-gradient-to-br from-accent-200/20 to-primary-200/20 rounded-full blur-xl"></div>

        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
              ⭐ 4.9/5 sterren • 500+ tevreden klanten
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              Wat onze <span className="text-primary-600">klanten</span> zeggen
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Ontdek waarom klanten ons aanbevelen en steeds terugkomen
            </p>
          </div>

          {/* Reviews Carousel Container */}
          <div className="relative overflow-hidden">
            <div
              className="flex transition-transform duration-1000 ease-in-out"
              style={{ transform: `translateX(-${currentReviewIndex * (100 / 3)}%)` }}
            >
              {reviews.map((review, index) => (
                <div key={index} className="w-1/3 flex-shrink-0 px-3">
                  <Card className="group relative overflow-hidden bg-white/90 backdrop-blur-sm border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 h-full">
                    <CardContent className="p-6 relative h-full flex flex-col">
                      {/* Star rating */}
                      <div className="flex items-center mb-4">
                        <div className="flex space-x-1">
                          {[...Array(review.rating)].map((_, i) => (
                            <span key={i} className="text-accent-500 text-lg">★</span>
                          ))}
                        </div>
                        <span className="ml-2 text-sm font-medium text-gray-600">{review.rating}.0</span>
                      </div>

                      {/* Review text */}
                      <blockquote className="text-gray-700 mb-6 leading-relaxed italic flex-grow">
                        "{review.text}"
                      </blockquote>

                      {/* Customer info */}
                      <div className="flex items-center mt-auto">
                        <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                          {review.name.charAt(0)}
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">{review.name}</p>
                          <p className="text-sm text-gray-500">Tevreden klant</p>
                        </div>
                      </div>

                      {/* Hover accent */}
                      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 to-accent-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>

          {/* Carousel Indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: reviews.length - 2 }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentReviewIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentReviewIndex
                    ? 'bg-primary-600 scale-125'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link to="/beoordelingen">
              <Button variant="outline" className="border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg">
                Meer beoordelingen
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Kies een vakman en vind je klus</h2>
            <p className="text-xl text-gray-600">Van A tot Z, wij pakken elke klus aan</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <Card key={index} className="group hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 bg-white border-0 shadow-sm">
                  <CardContent className="p-6 text-center">
                    <div className="mb-4 flex justify-center">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center group-hover:bg-blue-50 transition-colors duration-300">
                        <IconComponent className="w-6 h-6 text-gray-600 group-hover:text-blue-600 transition-colors duration-300" />
                      </div>
                    </div>
                    <h3 className="text-sm font-semibold text-gray-900 mb-2">{service.title}</h3>
                    <p className="text-xs text-gray-500 leading-relaxed">{service.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
          <div className="text-center mt-8">
            <Link to="/diensten">
              <Button variant="outline" className="border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg">
                Alle diensten bekijken
              </Button>
            </Link>
          </div>
        </div>
      </section>



      <Footer />
    </div>
  );
};

export default Index;
