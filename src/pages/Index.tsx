
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Shield, Zap, Award, Star, Clock, Wrench } from 'lucide-react';

const Index = () => {
  const features = [
    {
      icon: Shield,
      title: "100% Betrouwbaar",
      description: "Gecertificeerde vakmannen • Verzekerd • Garantie op werk",
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: Zap,
      title: "Supersnel Service",
      description: "Binnen 24u reactie • Spoedklussen mogelijk • Flexibele planning",
      color: "from-orange-500 to-orange-600"
    },
    {
      icon: Award,
      title: "Premium Kwaliteit",
      description: "15+ jaar ervaring • 4.9★ klantbeoordeling • Vakmanschap gegarandeerd",
      color: "from-green-500 to-green-600"
    },
    {
      icon: Star,
      title: "Transparant & Eerlijk",
      description: "Vaste tarieven • <PERSON><PERSON> verrassing<PERSON> • Gratis offerte vooraf",
      color: "from-purple-500 to-purple-600"
    }
  ];

  const services = [
    {
      title: "Loodgieterswerk",
      description: "Van lekkages tot complete installaties",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop"
    },
    {
      title: "Elektriciteit", 
      description: "Veilige elektrische installaties en reparaties",
      image: "https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=400&h=300&fit=crop"
    },
    {
      title: "Schilderwerk",
      description: "Binnen- en buitenschilderwerk",
      image: "https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop"
    }
  ];

  const reviews = [
    {
      name: "Maria van den Berg",
      text: "Uitstekend werk geleverd! Snel, netjes en voor een eerlijke prijs.",
      rating: 5
    },
    {
      name: "Jan Hendriks", 
      text: "Zeer tevreden met de service. Punctueel en vakkundig.",
      rating: 5
    },
    {
      name: "Linda de Vries",
      text: "Aanrader! Professioneel en betrouwbaar.",
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-accent-500/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="animate-fade-in">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Uw klus,
                <span className="text-primary-600"> onze expertise</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Van kleine reparaties tot grote verbouwingen - wij pakken elke klus 
                professioneel aan. Betrouwbaar, snel en tegen een eerlijke prijs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/klus-plaatsen">
                  <Button className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                    Wat is uw klus?
                  </Button>
                </Link>
                <Link to="/afspraak-maken">
                  <Button variant="outline" className="border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                    Direct een afspraak
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative animate-float">
              <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-8 border border-white/30">
                <img
                  src="/Handyman (1).png"
                  alt="Professionele klusman met gereedschap - meerdere armen met verschillende tools"
                  className="w-full h-80 object-cover rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Waarom <span className="text-primary-600">ASklussen.nl</span>?
            </h2>
            <p className="text-lg text-gray-600 max-w-xl mx-auto">
              De slimme keuze voor al uw klussen. Professioneel, betrouwbaar en altijd scherp geprijsd.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="group relative overflow-hidden bg-white border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3">
                <CardContent className="p-8">
                  <div className="flex items-start space-x-4">
                    <div className={`w-14 h-14 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}>
                      <feature.icon className="w-7 h-7 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                  <div className={`absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r ${feature.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300`}></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Onze Diensten</h2>
            <p className="text-xl text-gray-600">Van A tot Z, wij pakken elke klus aan</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                <div className="relative">
                  <img 
                    src={service.image}
                    alt={service.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{service.title}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="text-center mt-8">
            <Link to="/diensten">
              <Button variant="outline" className="border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg">
                Alle diensten bekijken
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="py-16 bg-gradient-to-r from-primary-50 to-accent-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Wat onze klanten zeggen</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {reviews.map((review, index) => (
              <Card key={index} className="p-6 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
                <CardContent className="pt-6">
                  <div className="flex mb-4">
                    {[...Array(review.rating)].map((_, i) => (
                      <span key={i} className="text-yellow-400 text-lg">⭐</span>
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4 italic">"{review.text}"</p>
                  <p className="font-semibold text-gray-900">{review.name}</p>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="text-center mt-8">
            <Link to="/beoordelingen">
              <Button variant="outline" className="border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg">
                Meer beoordelingen
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
