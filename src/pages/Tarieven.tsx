
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Link } from 'react-router-dom';
import { Check, Clock, Users } from 'lucide-react';

const Tarieven = () => {
  const pricingPlans = [
    {
      title: "Kleine Klussen",
      description: "Voor snelle reparaties en kleine onderhoudswerkzaamheden",
      price: "€ 45",
      period: "per uur",
      features: [
        "Geen voorrijkosten binnen 15 km",
        "Minimaal 1 uur facturatie",
        "Inclusief basis gereedschap",
        "Kleine reparaties",
        "Montage werk"
      ],
      color: "from-green-500 to-green-600",
      popular: false
    },
    {
      title: "Standaard Klussen", 
      description: "Voor reguliere klussen en projecten",
      price: "€ 55",
      period: "per uur",
      features: [
        "Geen voorrijkosten binnen 25 km", 
        "Professioneel gereedschap",
        "Materiaal advies",
        "Kwaliteitsgarantie",
        "Nazorg service"
      ],
      color: "from-primary-500 to-primary-600",
      popular: true
    },
    {
      title: "Grote Projecten",
      description: "Voor uitgebreide verbouwingen en renovaties",
      price: "Op maat",
      period: "vaste prijs",
      features: [
        "Gratis offerte",
        "Projectplanning",
        "Materiaal inkoop",
        "Eindschoonmaak",
        "Garantie op werk"
      ],
      color: "from-purple-500 to-purple-600", 
      popular: false
    }
  ];

  const additionalCosts = [
    {
      item: "Voorrijkosten",
      description: "Buiten normale werkgebied",
      price: "€ 0,35 per km"
    },
    {
      item: "Avond/weekend",
      description: "Buiten kantooruren",
      price: "+ 25% toeslag"
    },
    {
      item: "Spoed/noodgevallen",
      description: "24/7 beschikbaarheid",
      price: "+ 50% toeslag"
    },
    {
      item: "Materiaalkosten",
      description: "Tegen inkoopprijs",
      price: "+ 15% opslag"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      <div className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12 animate-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Transparante <span className="text-primary-600">Tarieven</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Eerlijke prijzen zonder verrassingen. Bekijk onze tarieven en vraag een vrijblijvende offerte aan.
            </p>
          </div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {pricingPlans.map((plan, index) => (
              <Card key={index} className={`relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 ${plan.popular ? 'ring-2 ring-primary-500' : ''}`}>
                {plan.popular && (
                  <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-primary-500 to-primary-600 text-white text-center py-2 text-sm font-semibold">
                    Meest gekozen
                  </div>
                )}
                <CardHeader className={`text-center p-8 ${plan.popular ? 'pt-12' : ''}`}>
                  <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${plan.color} flex items-center justify-center mx-auto mb-4`}>
                    <Clock className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-2">{plan.title}</CardTitle>
                  <p className="text-gray-600 mb-4">{plan.description}</p>
                  <div className="text-4xl font-bold text-gray-900 mb-1">{plan.price}</div>
                  <div className="text-gray-500">{plan.period}</div>
                </CardHeader>
                <CardContent className="p-8 pt-0">
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-center text-gray-700">
                        <Check className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Link to="/klus-plaatsen">
                    <Button className={`w-full bg-gradient-to-r ${plan.color} hover:opacity-90 text-white py-3 transition-all duration-300`}>
                      Offerte aanvragen
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Additional Costs */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl mb-16">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-center text-gray-900">Aanvullende Kosten</CardTitle>
              <p className="text-center text-gray-600">Transparantie is belangrijk voor ons</p>
            </CardHeader>
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {additionalCosts.map((cost, index) => (
                  <div key={index} className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h3 className="font-semibold text-gray-900">{cost.item}</h3>
                      <p className="text-sm text-gray-600">{cost.description}</p>
                    </div>
                    <div className="text-lg font-bold text-primary-600">{cost.price}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Guarantee Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white border-0 shadow-xl">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Check className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold mb-4">Kwaliteitsgarantie</h3>
                <p className="text-green-100">
                  Wij staan achter ons werk. Op alle uitgevoerde werkzaamheden geven we garantie.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0 shadow-xl">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold mb-4">Geen verrassingen</h3>
                <p className="text-blue-100">
                  Alle kosten worden vooraf duidelijk gecommuniceerd. Geen onverwachte bijkomende kosten.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <Card className="bg-gradient-to-r from-primary-600 to-primary-800 text-white border-0 shadow-xl">
              <CardContent className="p-12">
                <h2 className="text-3xl font-bold mb-4">Vraag een vrijblijvende offerte aan</h2>
                <p className="text-xl text-primary-100 mb-8">
                  Ontdek wat wij voor u kunnen betekenen. Geen verplichtingen, wel duidelijkheid.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link to="/klus-plaatsen">
                    <Button className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-3 text-lg">
                      Offerte aanvragen
                    </Button>
                  </Link>
                  <Link to="/contact">
                    <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600 px-8 py-3 text-lg">
                      Bel voor advies
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Tarieven;
