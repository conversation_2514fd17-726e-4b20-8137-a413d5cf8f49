
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Users } from 'lucide-react';

const Beoordelingen = () => {
  const reviews = [
    {
      name: "<PERSON>",
      location: "Amsterdam",
      service: "Loodgieterswerk",
      rating: 5,
      date: "2024-01-15",
      text: "Uitstekend werk geleverd! De lekkage in mijn badkamer is vakkundig gerepareerd. Snel, netjes en voor een eerlijke prijs. Zeer aan te bevelen!",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face"
    },
    {
      name: "<PERSON>",
      location: "Utrecht", 
      service: "Elektriciteit",
      rating: 5,
      date: "2024-01-10",
      text: "Zeer tevreden met de service. Nieuwe stopcontacten geïnstalleerd in de keuken. Punctueel, vakkundig en alles netjes opgeruimd.",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
    },
    {
      name: "Linda de Vries",
      location: "Rotterdam",
      service: "Schilderwerk", 
      rating: 5,
      date: "2024-01-08",
      text: "Fantastisch schilderwerk in onze woonkamer. Professioneel, betrouwbaar en een mooi resultaat. Zeker een aanrader!",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face"
    },
    {
      name: "Piet Jansen",
      location: "Den Haag",
      service: "Montage",
      rating: 5,
      date: "2024-01-05",
      text: "IKEA keuken perfect gemonteerd. Zeer vakkundig werk en goede communicatie. Precies op tijd en binnen budget.",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"
    },
    {
      name: "Sandra Bakker",
      location: "Eindhoven",
      service: "Onderhoud",
      rating: 5,
      date: "2024-01-02",
      text: "Regelmatig onderhoud aan ons huis. Altijd betrouwbaar, kwaliteit en goede prijzen. Al jaren klant en zeer tevreden.",
      avatar: "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=100&h=100&fit=crop&crop=face"
    },
    {
      name: "Robert van Dijk",
      location: "Groningen",
      service: "Tegels & Vloeren",
      rating: 5,
      date: "2023-12-28",
      text: "Badkamertegels vervangen. Prachtig resultaat en zeer netjes uitgevoerd. Communicatie was uitstekend van begin tot eind.",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face"
    }
  ];

  const stats = [
    { number: "500+", label: "Tevreden klanten" },
    { number: "1000+", label: "Uitgevoerde klussen" },
    { number: "4.9/5", label: "Gemiddelde beoordeling" },
    { number: "98%", label: "Aanbevelingspercentage" }
  ];

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('nl-NL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      <div className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16 animate-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Klant<span className="text-primary-600">beoordelingen</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Lees wat onze klanten over ons zeggen. Echte ervaringen van echte mensen.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center p-6 bg-white/70 backdrop-blur-sm border-0 shadow-lg">
                <CardContent className="pt-6">
                  <div className="text-3xl font-bold text-primary-600 mb-2">{stat.number}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Reviews Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {reviews.map((review, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <CardContent className="p-6">
                  {/* Rating */}
                  <div className="flex mb-4">
                    {[...Array(review.rating)].map((_, i) => (
                      <span key={i} className="text-yellow-400 text-lg">⭐</span>
                    ))}
                  </div>
                  
                  {/* Review Text */}
                  <p className="text-gray-700 mb-6 italic leading-relaxed">
                    "{review.text}"
                  </p>
                  
                  {/* Customer Info */}
                  <div className="flex items-center space-x-3">
                    <img 
                      src={review.avatar}
                      alt={review.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <p className="font-semibold text-gray-900">{review.name}</p>
                      <p className="text-sm text-gray-500">{review.location}</p>
                      <p className="text-sm text-primary-600">{review.service}</p>
                    </div>
                  </div>
                  
                  {/* Date */}
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <p className="text-xs text-gray-500">{formatDate(review.date)}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* CTA Section */}
          <Card className="bg-gradient-to-r from-primary-600 to-primary-800 text-white border-0 shadow-xl">
            <CardContent className="p-12 text-center">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl font-bold mb-4">Word ook een tevreden klant</h2>
              <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
                Sluit u aan bij onze vele tevreden klanten. Ervaar zelf de kwaliteit en service van ASklussen.nl.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/klus-plaatsen" className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105">
                  Plaats uw klus
                </a>
                <a href="/contact" className="border-2 border-white text-white hover:bg-white hover:text-primary-600 px-8 py-3 rounded-lg font-medium transition-all duration-200">
                  Contact opnemen
                </a>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Beoordelingen;
