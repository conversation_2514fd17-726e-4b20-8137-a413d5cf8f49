
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Link } from 'react-router-dom';
import { Home, Clock, Check, Users, ArrowRight } from 'lucide-react';

const Diensten = () => {
  const services = [
    {
      title: "Loodgieterswerk",
      description: "Professionele loodgietersklussen uitgevoerd door ervaren vakmannen.",
      details: [
        "Lekkage reparaties",
        "Kraan installaties", 
        "Toilet en badkamer",
        "CV-ketels en radiatoren",
        "Verstoppingen verhelpen",
        "Waterleiding aanleg"
      ],
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500&h=300&fit=crop",
      icon: "🔧",
      link: "/diensten/loodgieterswerk",
      price: "vanaf €45/uur"
    },
    {
      title: "Elektriciteit",
      description: "Veilige en betrouwbare elektrische installaties en reparaties.",
      details: [
        "Stopcontacten en schakelaars",
        "Verlichting installeren",
        "Groepenkast uitbreiden",
        "Storingen oplossen", 
        "Periodieke keuringen",
        "Smart home installatie"
      ],
      image: "https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=500&h=300&fit=crop",
      icon: "⚡",
      link: "/diensten/elektriciteit",
      price: "vanaf €50/uur"
    },
    {
      title: "Schilderwerk",
      description: "Binnen- en buitenschilderwerk voor een frisse uitstraling.",
      details: [
        "Muren en plafonds",
        "Houtwerk en kozijnen",
        "Behang aanbrengen",
        "Gevelreiniging",
        "Voorbehandeling",
        "Decoratief schilderen"
      ],
      image: "https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=500&h=300&fit=crop",
      icon: "🎨",
      link: "/diensten/schilderwerk",
      price: "vanaf €35/uur"
    },
    {
      title: "Montage & Assemblage",
      description: "Professionele montage van meubilair en apparatuur.",
      details: [
        "IKEA meubels",
        "Keukens monteren",
        "TV-ophang",
        "Wandkasten",
        "Tuinmeubilair",
        "Fitnessapparatuur"
      ],
      image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=500&h=300&fit=crop",
      icon: "🔨",
      link: "/diensten/montage",
      price: "vanaf €40/uur"
    },
    {
      title: "Onderhoud & Reparatie",
      description: "Regulier onderhoud en reparaties voor uw woning.",
      details: [
        "Preventief onderhoud",
        "Kleine reparaties",
        "Winterklaar maken",
        "Dakgoot reiniging",
        "Kozijn onderhoud",
        "Hang- en sluitwerk"
      ],
      image: "https://images.unsplash.com/photo-1503387762-592deb58ef4e?w=500&h=300&fit=crop",
      icon: "🛠️",
      link: "/diensten/onderhoud",
      price: "vanaf €38/uur"
    },
    {
      title: "Tegels & Vloeren",
      description: "Professionele tegelwerk en vloer installaties.",
      details: [
        "Badkamertegels",
        "Keukentegels", 
        "Laminaat leggen",
        "PVC vloeren",
        "Plinten plaatsen",
        "Vloerverwarming"
      ],
      image: "https://images.unsplash.com/photo-1489269637500-aa0e75768394?w=500&h=300&fit=crop",
      icon: "🏠",
      link: "/diensten/tegels",
      price: "vanaf €42/uur"
    },
    {
      title: "Tuinonderhoud",
      description: "Complete tuinverzorging en landschapsarchitectuur.",
      details: [
        "Gazon maaien en verzorgen",
        "Hagen snoeien",
        "Onkruid verwijderen",
        "Planten en beplanting",
        "Tuinontwerp",
        "Seizoensonderhoud"
      ],
      image: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=500&h=300&fit=crop",
      icon: "🌱",
      link: "/diensten/tuinonderhoud",
      price: "vanaf €30/uur"
    },
    {
      title: "Verhuizing & Transport",
      description: "Professionele verhuizingen en transport diensten.",
      details: [
        "Volledige verhuizingen",
        "Inpakservice",
        "Meubeltransport",
        "Opslag mogelijkheden",
        "Montage en demontage",
        "Bedrijfsverhuizingen"
      ],
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500&h=300&fit=crop",
      icon: "🚚",
      link: "/diensten/verhuizing",
      price: "vanaf €65/uur"
    },
    {
      title: "Schoonmaak Diensten",
      description: "Professionele schoonmaak voor particulieren en bedrijven.",
      details: [
        "Huishoudelijke schoonmaak",
        "Kantoorschoonmaak",
        "Eindschoonmaak",
        "Ramen wassen",
        "Tapijt reiniging",
        "Periodieke schoonmaak"
      ],
      image: "https://images.unsplash.com/photo-1563453392212-326f5e854473?w=500&h=300&fit=crop",
      icon: "🧽",
      link: "/diensten/schoonmaak",
      price: "vanaf €25/uur"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center animate-fade-in">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Onze <span className="text-primary-600">Diensten</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Van kleine reparaties tot complete projecten - wij bieden een volledig scala aan 
              professionele diensten voor uw woning en bedrijf.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/offerte">
                <Button className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-3 text-lg">
                  Gratis Offerte Aanvragen
                </Button>
              </Link>
              <Link to="/klus-plaatsen">
                <Button variant="outline" className="border-primary-600 text-primary-600 hover:bg-primary-50 px-8 py-3 text-lg">
                  Direct Klus Plaatsen
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                <div className="relative">
                  <img 
                    src={service.image}
                    alt={service.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-2xl">
                    {service.icon}
                  </div>
                  <div className="absolute top-4 right-4 bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    {service.price}
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2 mb-6">
                    {service.details.map((detail, i) => (
                      <li key={i} className="flex items-center text-sm text-gray-700">
                        <Check className="w-4 h-4 text-primary-600 mr-2 flex-shrink-0" />
                        {detail}
                      </li>
                    ))}
                  </ul>
                  <div className="flex space-x-2">
                    <Link to={service.link} className="flex-1">
                      <Button className="w-full bg-primary-600 hover:bg-primary-700 text-white">
                        Meer Info <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                    <Link to="/offerte">
                      <Button variant="outline" className="border-accent-500 text-accent-500 hover:bg-accent-50">
                        Offerte
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-primary-600 to-primary-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Staat uw klus er niet bij?
          </h2>
          <p className="text-xl text-primary-100 mb-8">
            Geen probleem! Wij kijken graag wat we voor u kunnen betekenen.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/klus-plaatsen">
              <Button className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-3 text-lg">
                Beschrijf uw klus
              </Button>
            </Link>
            <Link to="/contact">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600 px-8 py-3 text-lg">
                Direct contact
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Diensten;
